.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 140rpx;
}

.skillsList {
  background: #fff;
  padding: 0 30rpx;
}

.skillItem {
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
}

.skillLabel {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.skillInput {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #eee;
}

.buttonGroup {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20rpx 30rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 999;
}

.saveBtn, .deleteBtn {
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.saveBtn {
  flex: 3;
  background: #4B8BF5;
  color: #fff;
  margin-right: 40rpx;
}

.deleteBtn {
  flex: 1;
  background: #f5f5f5;
  color: #666;
} 