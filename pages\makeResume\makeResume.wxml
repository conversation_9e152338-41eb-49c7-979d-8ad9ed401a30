<view class="container">
  <!-- 顶部导航栏 -->
  <view class="nav-bar">
  </view>

  <!-- 主要内容区域 -->
  <view class="content">
    <!-- 欢迎语 -->
    <view class="welcomeSection">
      <view class="welcomeTitle">欢迎使用，</view>
      <view class="welcomeDesc">Hi, 根据自身需求填写简历，简历模板只显示已填写内容，不填写不显示。填写完成后选择模板生成简历。</view>
    </view>

    <!-- 调试信息 -->
    <!-- <view style="background: #f0f0f0; padding: 10px; margin: 10px; font-size: 12px;">
      <text>调试信息：</text>
      <text>activeModules长度: {{activeModules.length}}</text>
      <text>availableModulesToAdd长度: {{availableModulesToAdd.length}}</text>
      <text>basicInfo.name: {{basicInfo.name}}</text>
    </view>
    -->

    <!-- 基本信息卡片 -->
    <view class="infoCard">
      <block wx:for="{{activeModules}}" wx:key="id">
        <!-- 基本信息 -->
        <block wx:if="{{item.type === 'basicInfo'}}">
          <view class="infoSection">
            <view class="sectionContent">
              <view class="mainInfo" bindtap="handleModuleClick" data-type="basicInfo">
                <text class="name">{{basicInfo.name || '点击添加'}}</text>
                <text class="subText">{{basicInfo.gender ? basicInfo.gender + ' | ' : ''}}{{basicInfo.phone || ''}}</text>
              </view>
              <view class="addPhoto" bindtap="chooseImage">
                <block wx:if="{{!basicInfo.photoUrl}}">
                  <text class="addIcon">+</text>
                  <text class="addText">添加证件照</text>
                </block>
                <image wx:else class="photo" src="{{basicInfo.photoUrl}}" mode="aspectFill"></image>
              </view>
            </view>
          </view>
        </block>

        <!-- 求职意向 -->
        <block wx:elif="{{item.type === 'jobIntention'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="jobIntention">
            <view class="sectionTitle">求职意向</view>
            <view class="sectionContent">
              <view class="mainInfo">
                <text class="subText">{{jobIntention.position ? jobIntention.position + ' | ' : ''}}{{jobIntention.city ? jobIntention.city + ' | ' : ''}}{{jobIntention.salary ? jobIntention.salary + ' | ' : ''}}{{jobIntention.status || '点击添加'}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 教育经历 -->
        <block wx:elif="{{item.type === 'education'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="education">
            <view class="sectionTitle">教育经历</view>
            <view class="sectionContent" wx:for="{{education}}" wx:key="index" wx:for-item="eduItem">
              <view class="mainInfo">
                <text class="school">{{eduItem.school}}</text>
                <text class="subText">{{eduItem.degree}} {{eduItem.major}}</text>
              </view>
              <text class="dateText">{{eduItem.startDate}} - {{eduItem.endDate}}</text>
            </view>
          </view>
        </block>

        <!-- 在校经历 -->
        <block wx:elif="{{item.type === 'school'}}">
          <view class="infoSection school-section" bindtap="handleModuleClick" data-type="school">
            <view class="sectionTitle">在校经历</view>
            <view class="sectionContent" wx:for="{{school}}" wx:key="index" wx:for-item="schoolItem">
              <view class="mainInfo">
                <view class="titleRow">
                  <text class="title">{{schoolItem.title}}</text>
                    <text class="dateText">{{schoolItem.startDate}} - {{schoolItem.endDate}}</text>
                </view>
                <text class="roleText">{{schoolItem.role}}</text>
                <text class="description-preview" wx:if="{{schoolItem.description}}">{{schoolItem.description}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 实习经历 -->
        <block wx:elif="{{item.type === 'internship'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="internship">
            <view class="sectionTitle">实习经历</view>
            <view class="sectionContent" wx:for="{{internship}}" wx:key="index" wx:for-item="internItem">
              <view class="mainInfo">
                <text class="school">{{internItem.company}}</text>
                <text class="subText">{{internItem.position}}</text>
              </view>
              <text class="dateText">{{internItem.startDate}} - {{internItem.endDate}}</text>
            </view>
          </view>
        </block>

        <!-- 工作经历 -->
        <block wx:elif="{{item.type === 'work'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="work">
            <view class="sectionTitle">工作经历</view>
            <view class="sectionContent" wx:for="{{work}}" wx:key="index" wx:for-item="workItem">
              <view class="mainInfo">
                <text class="school">{{workItem.company}}</text>
                <text class="subText">{{workItem.position}}</text>
              </view>
              <text class="dateText">{{workItem.startDate}} - {{workItem.endDate}}</text>
            </view>
          </view>
        </block>

        <!-- 项目经历 -->
        <block wx:elif="{{item.type === 'project'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="project">
            <view class="sectionTitle">项目经历</view>
            <view class="sectionContent" wx:for="{{project}}" wx:key="index" wx:for-item="projectItem">
              <view class="mainInfo">
                <text class="school">{{projectItem.projectName}}</text>
                <text class="subText">{{projectItem.role}}</text>
              </view>
              <text class="dateText">{{projectItem.startDate}} - {{projectItem.endDate}}</text>
            </view>
          </view>
        </block>

        <!-- 职业技能 -->
        <block wx:elif="{{item.type === 'skills'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="skills">
            <view class="sectionTitle">技能特长</view>
            <view class="sectionContent">
              <view class="mainInfo">
                <view class="skills">
                  <text class="skillItem" wx:for="{{skills}}" wx:key="index" wx:for-item="skillItem">{{skillItem}}</text>
                </view>
              </view>
            </view>
          </view>
        </block>

        <!-- 奖项证书 -->
        <block wx:elif="{{item.type === 'awards'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="awards">
            <view class="sectionTitle">奖项证书</view>
            <view class="sectionContent">
              <view class="mainInfo">
                <view class="skills">
                  <text class="skillItem" wx:for="{{awards}}" wx:key="index" wx:for-item="awardItem">{{awardItem}}</text>
                </view>
              </view>
            </view>
          </view>
        </block>

        <!-- 兴趣爱好 -->
        <block wx:elif="{{item.type === 'interests'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="interests">
            <view class="sectionTitle">兴趣爱好</view>
            <view class="sectionContent">
              <view class="mainInfo">
                <view class="skills">
                  <text class="skillItem" wx:for="{{interests}}" wx:key="index" wx:for-item="interestItem">{{interestItem}}</text>
                </view>
              </view>
            </view>
          </view>
        </block>

        <!-- 自我评价 -->
        <block wx:elif="{{item.type === 'evaluation'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="evaluation">
            <view class="sectionTitle">自我评价</view>
            <view class="sectionContent">
              <view class="mainInfo">
                <text class="description-preview" user-select="text">{{evaluation[0].content}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 自定义模块一 -->
        <block wx:elif="{{item.type === 'custom1'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="custom1">
            <view class="sectionTitle">{{custom1[0].customName || '自定义模块一'}}</view>
            <view class="sectionContent">
              <view class="mainInfo">
                <text class="subText">{{custom1[0].role}}</text>
                <text class="description-preview" wx:if="{{custom1[0].content}}" user-select="text">{{custom1[0].content}}</text>
              </view>
              <text class="dateText">{{custom1[0].startDate}} - {{custom1[0].endDate}}</text>
            </view>
          </view>
        </block>

        <!-- 自定义模块二 -->
        <block wx:elif="{{item.type === 'custom2'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="custom2">
            <view class="sectionTitle">{{custom2[0].customName || '自定义模块二'}}</view>
            <view class="sectionContent">
              <view class="mainInfo">
                <text class="subText">{{custom2[0].role}}</text>
                <text class="description-preview" wx:if="{{custom2[0].content}}" user-select="text">{{custom2[0].content}}</text>
              </view>
              <text class="dateText">{{custom2[0].startDate}} - {{custom2[0].endDate}}</text>
            </view>
          </view>
        </block>

        <!-- 自定义模块三 -->
        <block wx:elif="{{item.type === 'custom3'}}">
          <view class="infoSection" bindtap="handleModuleClick" data-type="custom3">
            <view class="sectionTitle">{{custom3[0].customName || '自定义模块三'}}</view>
            <view class="sectionContent">
              <view class="mainInfo">
                <text class="subText">{{custom3[0].role}}</text>
                <text class="description-preview" wx:if="{{custom3[0].content}}" user-select="text">{{custom3[0].content}}</text>
              </view>
              <text class="dateText">{{custom3[0].startDate}} - {{custom3[0].endDate}}</text>
            </view>
          </view>
        </block>
      </block>
    </view>

    <!-- 添加更多模块 -->
    <view class="modulesSection">
      <view class="sectionTitle">添加更多模块</view>
      <view class="modulesGrid">
        <view class="moduleItem addItem"
              wx:for="{{availableModulesToAdd}}"
              wx:key="id"
              bindtap="addModule"
              data-type="{{item.type}}">
          <!-- <image class="moduleIcon" src="/images/add_icon.png"></image> -->
          <!-- <text class="addIcon">+</text> -->
          <view class="moduleName">{{item.name}}</view>
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="bottomBar">
      <view class="actionButtons">
        <view class="actionBtn" bindtap="handleModules">
          <image class="icon" src="./images/moduleManage.png" mode="aspectFit"></image>
          <text>模块管理</text>
        </view>
        <view class="actionBtn" bindtap="handleAI">
          <image class="icon" src="./images/aiRobot.png" mode="aspectFit"></image>
          <text>AI智能优化</text>
        </view>
        <view class="actionBtn" bindtap="generateResume">
          <image class="icon" src="./images/generate.png" mode="aspectFit"></image>
          <text>生成简历</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 隐藏的Canvas用于图片压缩 -->
  <canvas canvas-id="compressCanvas" style="position: fixed; top: -1000px; left: -1000px; width: 400px; height: 600px;"></canvas>

  <!-- 隐藏的Canvas用于智能裁剪 -->
  <canvas canvas-id="smartCropCanvas" style="position: fixed; top: -2000px; left: -1000px; width: 120px; height: 150px;"></canvas>
</view>