.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 140rpx;
}

.formGroup {
  background: #fff;
  padding: 0 30rpx;
}

.sectionTitle {
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #999;
  position: relative;
}

.formItem {
  display: flex;
  align-items: center;
  min-height: 100rpx;
  border-bottom: 1rpx solid #eee;
  position: relative;
}

.label {
  width: 180rpx;
  font-size: 28rpx;
  color: #333;
  padding-right: 20rpx;
}

.input {
  flex: 1;
  height: 88rpx;
  font-size: 28rpx;
  color: #333;
}

.picker {
  flex: 1;
  height: 88rpx;
}

.pickerArea {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  background: #fff;
  padding-right: 20rpx;
}

.pickerArea:active {
  background: #f8f8f8;
}

.pickerText {
  font-size: 28rpx;
  color: #333;
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
}

.pickerText:empty::before {
  content: '请选择';
  color: #999;
}

.deleteIcon {
  padding: 20rpx;
  color: #999;
  font-size: 32rpx;
}

/* 自定义字段的特殊样式 */
.formItem .customTitle {
  width: 200rpx;
  flex: none;
  border-right: 1rpx solid #eee;
  margin-right: 20rpx;
}

/* 必填项标记 */
.formItem.required .label::before {
  content: '*';
  color: #f56c6c;
  margin-right: 4rpx;
}

/* 底部按钮组 */
.buttonGroup {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  padding: 20rpx 30rpx;
  display: flex;
  align-items: center;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
  box-sizing: border-box;
  z-index: 100;
}

.saveBtn {
  flex: 3;
  height: 80rpx;
  line-height: 80rpx;
  background: #4B8BF5;
  color: #fff;
  font-size: 28rpx;
  border-radius: 40rpx;
  margin-right: 40rpx;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
}

.saveBtn::after {
  display: none;
}

.deleteBtn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  background: #f5f5f5;
  color: #666;
  font-size: 28rpx;
  border-radius: 40rpx;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
}

.deleteBtn::after {
  display: none;
}

/* 输入框placeholder样式 */
.input::placeholder {
  color: #999;
}

/* 选择器未选择时的样式 */
.pickerText.empty {
  color: #999;
}

/* 表单项hover效果 */
.formItem:active {
  background-color: #f9f9f9;
}

/* 删除图标hover效果 */
.deleteIcon:active {
  opacity: 0.7;
}

/* 按钮hover效果 */
.saveBtn:active {
  opacity: 0.9;
}

.deleteBtn:active {
  background: #eee;
}

/* 证件照上传样式 */
.photoSection {
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
  display: none;
}

.photoContainer {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
}

.photoUpload {
  width: 180rpx;
  height: 240rpx;
  border: 2rpx dashed #999;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-right: 20rpx;
}

.photo {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

.uploadPlaceholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;
}

.uploadIcon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.uploadText {
  font-size: 24rpx;
  color: #999;
} 