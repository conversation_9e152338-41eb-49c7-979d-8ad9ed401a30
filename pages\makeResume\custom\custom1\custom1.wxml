<view class="container1">
  <!-- 顶部标题已经在导航栏显示 -->

  <!-- 名称输入区域 -->
  <view class="formItem1">
    <view class="label1 required">自定义名称一</view>
    <input class="input1"
           placeholder="请输入"
           value="{{custom1FormData.customName}}"
           bindinput="handleNameInput"/>
  </view>

  <!-- 时间选择区域 -->
  <view class="formItem1">
    <view class="label1">时间</view>
    <view class="datePicker1">
      <view class="dateSection1">
        <picker mode="date" fields="month" value="{{custom1FormData.startDate}}" bindchange="handleStartDateChange">
          <view class="picker1 {{custom1FormData.startDate ? '' : 'placeholder1'}}">
            {{custom1FormData.startDate || '请选择'}}
          </view>
        </picker>
      </view>
      <text class="separator1">至</text>
      <view class="dateSection1">
        <picker mode="date" fields="month" value="{{custom1FormData.endDate}}" bindchange="handleEndDateChange">
          <view class="picker1 {{custom1FormData.endDate ? '' : 'placeholder1'}}">
            {{custom1FormData.endDate || '请选择'}}
          </view>
        </picker>
      </view>
      <view class="nowBtn1" bindtap="setToNow">至今</view>
    </view>
  </view>

  <!-- 角色输入区域 -->
  <view class="formItem1">
    <view class="label1">角色</view>
    <input class="input1"
           placeholder="请输入(选填)"
           value="{{custom1FormData.role}}"
           bindinput="handleRoleInput"/>
  </view>

  <!-- 自定义内容区域 -->
  <view class="formItem1 contentArea1">
    <view class="label1">自定义内容</view>
    <textarea class="contentInput1"
              placeholder="请输入自定义的详细内容"
              value="{{custom1FormData.content}}"
              bindinput="handleContentInput"
              maxlength="-1"
              auto-height/>

  </view>

  <!-- 底部按钮组 -->
  <view class="buttonGroup1">
    <button class="saveBtn1" bindtap="saveContent">保存信息</button>
    <button class="deleteBtn1" bindtap="deleteContent">删除</button>
  </view>
</view>