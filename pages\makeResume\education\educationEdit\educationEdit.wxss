.container {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 140rpx;
}

.formGroup {
  background: #fff;
  padding: 0 30rpx;
}

.formItem {
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
}

.formItem:last-child {
  border-bottom: none;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  color: #333;
  padding: 0;
  background: transparent;
}

.input::placeholder {
  color: #999;
  font-size: 28rpx;
}

.picker {
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  color: #333;
}

.placeholder,
input::placeholder {
  color: #999;
  font-size: 28rpx;
}

.datePicker {
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 0 20rpx;
}

.dateSection {
  flex: 1;
  position: relative;
}

.picker {
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  color: #333;
  text-align: center;
}

.placeholder {
  color: #999;
}

.separator {
  margin: 0 20rpx;
  color: #666;
  font-size: 24rpx;
}

.nowBtn {
  padding: 10rpx 24rpx;
  background: #fff;
  color: #4B8BF5;
  font-size: 24rpx;
  border-radius: 30rpx;
  border: 1rpx solid #4B8BF5;
  margin-left: 20rpx;
}

.textarea {
  width: 100%;
  height: 300rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  border: 1rpx solid #eee;
  border-radius: 8rpx;
}

.buttonGroup {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20rpx 30rpx;
  background: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.1);
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
}

.saveBtn, .deleteBtn {
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 40rpx;
  font-size: 28rpx;
  line-height: 1;
}

.saveBtn {
  flex: 3;
  background: #4B8BF5;
  color: #fff;
  margin-right: 40rpx;
}

.deleteBtn {
  flex: 1;
  background: #f5f5f5;
  color: #666;
}

.degreePicker {
  width: 100%;
}

.degreePicker .picker {
  position: relative;
  padding-right: 40rpx;
}

.pickerArrow {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%) rotate(90deg);
  color: #999;
  font-size: 24rpx;
} 